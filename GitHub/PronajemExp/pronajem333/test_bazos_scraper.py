#!/usr/bin/env python3
"""
Test script pro Bazos.cz scraper
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.scraper_bazos import ScraperBazos
from disposition import Disposition


def test_bazos_scraper():
    """Test základní funkčnosti Bazos scraperu"""
    print("=== Test Bazos.cz Scraper ===")
    
    # Test s dispozicemi 2+1 a 2+kk
    dispositions = Disposition.FLAT_2KK | Disposition.FLAT_2
    scraper = ScraperBazos(dispositions)
    
    # Test základních vlastností
    print(f"✓ Scraper name: {scraper.name}")
    print(f"✓ Scraper color: {scraper.color}")
    print(f"✓ Scraper logo: {scraper.logo_url}")
    
    # Test detekce dispozic
    test_cases = [
        ("Pronájem bytu 2+kk 85 m²", True),
        ("Pronájem byt 2+1 v centru", True),
        ("Pronájem, byty/2+kk, 35 m2", True),
        ("Pronájem byt 1+kk", False),
        ("Pronájem bytu 3+1", False),
        ("2 +kk Brno", True),
        ("2+ 1 byt", True),
    ]
    
    print("\n--- Test detekce dispozic ---")
    for text, expected in test_cases:
        result = scraper._matches_required_dispositions(text)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{text}' -> {result} (expected: {expected})")
    
    # Test extrakce ceny
    print("\n--- Test extrakce ceny ---")
    price_tests = [
        ("18 950 Kč", "18950"),
        ("22000 Kč", "22000"),
        ("15.000 Kč", "15000"),
        ("Kč", "0"),
        ("", "0"),
    ]
    
    for price_text, expected in price_tests:
        result = scraper._extract_price(price_text.replace('Kč', '').strip())
        status = "✓" if result == expected else "✗"
        print(f"{status} '{price_text}' -> '{result}' (expected: '{expected}')")
    
    # Test scrapingu (pouze pokud je internetové připojení)
    print("\n--- Test scrapingu ---")
    try:
        offers = scraper.get_latest_offers()
        print(f"✓ Scraping successful: found {len(offers)} offers")
        
        if offers:
            print("\nPrvní nabídka:")
            offer = offers[0]
            print(f"  Title: {offer.title}")
            print(f"  Price: {offer.price} Kč")
            print(f"  Location: {offer.location}")
            print(f"  Link: {offer.link}")
            print(f"  Image: {offer.image_url}")
            
            # Ověření, že nabídka obsahuje požadovanou dispozici
            has_disposition = scraper._matches_required_dispositions(offer.title)
            status = "✓" if has_disposition else "✗"
            print(f"  {status} Contains required disposition: {has_disposition}")
            
    except Exception as e:
        print(f"✗ Scraping failed: {e}")
        return False
    
    print("\n=== Test completed successfully ===")
    return True


if __name__ == "__main__":
    success = test_bazos_scraper()
    sys.exit(0 if success else 1)
