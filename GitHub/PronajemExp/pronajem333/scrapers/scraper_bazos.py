import logging
import re
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup

from disposition import Disposition
from scrapers.rental_offer import RentalOffer
from scrapers.scraper_base import ScraperBase


class ScraperBazos(ScraperBase):

    name = "Bazos.cz"
    logo_url = "https://www.bazos.cz/obrazky/bazos.svg"
    color = 0x0066CC
    base_url = "https://reality.bazos.cz"

    # Bazos nemá přímé filtry pro dispozice, musíme filtrovat podle textu
    # Mapování slouží pro detekci dispozic v textu
    disposition_mapping = {
        Disposition.FLAT_1KK: ["1+kk", "1 +kk", "1+ kk", "1 + kk"],
        Disposition.FLAT_1: ["1+1", "1 +1", "1+ 1", "1 + 1"],
        Disposition.FLAT_2KK: ["2+kk", "2 +kk", "2+ kk", "2 + kk"],
        Disposition.FLAT_2: ["2+1", "2 +1", "2+ 1", "2 + 1"],
        Disposition.FLAT_3KK: ["3+kk", "3 +kk", "3+ kk", "3 + kk"],
        Disposition.FLAT_3: ["3+1", "3 +1", "3+ 1", "3 + 1"],
        Disposition.FLAT_4KK: ["4+kk", "4 +kk", "4+ kk", "4 + kk"],
        Disposition.FLAT_4: ["4+1", "4 +1", "4+ 1", "4 + 1"],
        Disposition.FLAT_5_UP: ["5+kk", "5 +kk", "5+ kk", "5 + kk", "5+1", "5 +1", "5+ 1", "5 + 1",
                               "6+kk", "6 +kk", "6+ kk", "6 + kk", "6+1", "6 +1", "6+ 1", "6 + 1"],
        Disposition.FLAT_OTHERS: ["atypick", "atyp", "nestandardn", "jiný", "ostatní"],
    }

    def get_dispositions_data(self) -> list:
        """Pro Bazos vracíme prázdný seznam, protože filtrujeme až po stažení"""
        return []

    def _detect_disposition_from_text(self, text: str) -> Disposition:
        """Detekuje dispozici z textu titulku nebo popisu"""
        text_lower = text.lower()

        detected_dispositions = Disposition.NONE

        for disposition, patterns in self.disposition_mapping.items():
            for pattern in patterns:
                if pattern.lower() in text_lower:
                    detected_dispositions |= disposition
                    break

        return detected_dispositions

    def _matches_required_dispositions(self, text: str) -> bool:
        """Kontroluje, zda text obsahuje některou z požadovaných dispozic"""
        detected = self._detect_disposition_from_text(text)
        return bool(detected & self.disposition)

    def _extract_price(self, price_text: str) -> str:
        """Extrahuje číselnou hodnotu ceny z textu"""
        if not price_text:
            return "0"

        # Odstraní všechny nečíselné znaky kromě číslic
        price_numbers = re.sub(r'[^\d]', '', price_text)
        return price_numbers if price_numbers else "0"

    def _create_link_to_offer(self, relative_url: str) -> str:
        """Vytvoří kompletní URL k nabídce"""
        if relative_url.startswith('http'):
            return relative_url
        return urljoin(self.base_url, relative_url)

    def build_response(self) -> requests.Response:
        """Vytvoří požadavek na Bazos.cz s filtrem pro Brno a cenový limit"""
        # URL s filtrem pro Brno (PSČ 60200) a cenový limit do 14000 Kč
        url = f"{self.base_url}/pronajmu/byt/"
        params = {
            'hledat': '',
            'rubriky': 'reality',
            'hlokalita': '60200',  # PSČ Brno
            'humkreis': '10',      # Okolí 10 km
            'cenaod': '',          # Cena od (prázdné)
            'cenado': '25000',     # Cena do 25000 Kč
            'Submit': 'Hledat',
            'order': '',
            'crp': '',
            'kitx': 'ano'
        }

        logging.debug("Bazos request: %s with params: %s", url, params)

        return requests.get(url, params=params, headers=self.headers)

    def get_latest_offers(self) -> list[RentalOffer]:
        """Načte a parsuje nabídky z Bazos.cz"""
        response = self.build_response()
        soup = BeautifulSoup(response.text, 'html.parser')

        items: list[RentalOffer] = []

        # Najdeme všechny <b> tagy s cenami
        price_tags = soup.find_all('b', string=lambda text: text and 'Kč' in text)
        logging.debug("Found %d price tags", len(price_tags))

        processed_links = set()  # Pro odstranění duplikátů

        for price_tag in price_tags:
            price_text = price_tag.get_text(strip=True)

            # Extrakce ceny
            price = self._extract_price(price_text.replace('Kč', '').strip())
            if price == "0":
                continue

            # Najdeme nejbližší inzerát - hledáme v kontejneru nebo předchozích sourozencích
            ad_link = None
            ad_title = ""

            # Nejprve hledáme v rodičovském kontejneru
            container = price_tag.parent
            while container and container.name != 'body':
                ad_links = container.find_all('a', href=lambda x: x and '/inzerat/' in x)
                if ad_links:
                    # Vezmeme první relevantní odkaz
                    for link in ad_links:
                        link_text = link.get_text(strip=True)
                        if link_text and len(link_text) > 10:
                            ad_link = link
                            ad_title = link_text
                            break
                    if ad_link:
                        break
                container = container.parent

            # Pokud nenajdeme v kontejneru, hledáme v předchozích sourozencích
            if not ad_link:
                current = price_tag.parent
                for _ in range(5):
                    prev_sibling = current.find_previous_sibling()
                    if prev_sibling:
                        ad_links = prev_sibling.find_all('a', href=lambda x: x and '/inzerat/' in x)
                        if ad_links:
                            for link in ad_links:
                                link_text = link.get_text(strip=True)
                                if link_text and len(link_text) > 10:
                                    ad_link = link
                                    ad_title = link_text
                                    break
                            if ad_link:
                                break
                        current = prev_sibling
                    else:
                        break

            if not ad_link:
                logging.debug("No ad link found for price: %s", price_text)
                continue

            href = ad_link.get('href', '')
            if href in processed_links:
                continue
            processed_links.add(href)

            # Kontrola, zda inzerát obsahuje požadovanou dispozici
            if not self._matches_required_dispositions(ad_title):
                logging.debug("Skipping ad - no matching disposition: %s", ad_title[:50])
                continue

            logging.debug("Found matching ad: %s with price: %s", ad_title[:50], price)

            # Extrakce lokality z titulku
            location = "Brno"
            location_patterns = [
                r'Brno[^,\n]*',
                r'(Brno\s*-\s*[^,\n]+)',
                r'(\d{3}\s*\d{2})',  # PSČ
            ]

            for pattern in location_patterns:
                location_match = re.search(pattern, ad_title)
                if location_match:
                    location = location_match.group(0).strip()
                    break

            # URL obrázku - hledáme v okolí inzerátu
            image_url = ""
            ad_container = ad_link.find_parent('div')
            if ad_container:
                img_tag = ad_container.find('img')
                if img_tag and img_tag.get('src'):
                    img_src = img_tag.get('src')
                    if img_src.startswith('http'):
                        image_url = img_src
                    else:
                        image_url = urljoin("https://www.bazos.cz", img_src)

            # Vytvoříme kompletní URL
            full_link = self._create_link_to_offer(href)

            items.append(RentalOffer(
                scraper=self,
                link=full_link,
                title=ad_title,
                location=location,
                price=price,
                image_url=image_url
            ))

        logging.info("Bazos scraper found %d offers", len(items))
        return items
